'use client';

import { Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Card } from '@/components/ui/card';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Todo } from '@/types';

interface TodoItemProps {
  todo: Todo;
  onToggle: (id: string) => void;
  onDelete: (id: string) => void;
}

export function TodoItem({ todo, onToggle, onDelete }: TodoItemProps) {
  return (
    <Card className="p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] border-2 border-border hover:border-primary/20">
      <div className="flex items-center gap-4">
        {/* 复选框 */}
        <Checkbox
          id={todo.id}
          checked={todo.status === 'completed'}
          onCheckedChange={() => onToggle(todo.id)}
          className="w-5 h-5 transition-all duration-200"
        />

        {/* 任务标题 */}
        <label
          htmlFor={todo.id}
          className={`flex-1 cursor-pointer text-base font-medium transition-all duration-300 ${
            todo.status === 'completed'
              ? 'line-through text-muted-foreground opacity-60'
              : 'text-foreground hover:text-primary'
          }`}
        >
          {todo.title}
        </label>

        {/* 状态指示器 */}
        {todo.status === 'completed' && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
            <span className="text-xs font-medium text-primary">已完成</span>
          </div>
        )}

        {/* 删除按钮 */}
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="glass"
              size="icon"
              className="h-9 w-9 hover:bg-destructive/20 hover:text-destructive hover:border-destructive/30 hover:shadow-lg hover:shadow-destructive/20"
            >
              <Trash2 className="h-4 w-4 relative z-10" />
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent className="max-w-md">
            <AlertDialogHeader className="space-y-4">
              <AlertDialogTitle className="text-xl font-semibold">确认删除</AlertDialogTitle>
              <AlertDialogDescription className="text-base text-muted-foreground leading-relaxed">
                您确定要删除这个任务吗？？？此操作无法撤销。
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="gap-3">
              <AlertDialogCancel className="font-semibold hover:scale-[1.02] transition-transform duration-200">
                取消
              </AlertDialogCancel>
              <AlertDialogAction
                onClick={() => onDelete(todo.id)}
                className="bg-gradient-to-r from-destructive via-destructive to-destructive/90 hover:shadow-lg hover:shadow-destructive/30 hover:scale-[1.02] active:scale-[0.98] font-semibold transition-all duration-300"
              >
                <span className="relative z-10">确认删除</span>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </Card>
  );
}
